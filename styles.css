/* Unique Text Styles */
@import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&display=swap');

/* Custom Text Effects */
.text-glow {
    text-shadow: 0 0 10px rgba(30, 136, 229, 0.5),
                 0 0 20px rgba(30, 136, 229, 0.3),
                 0 0 30px rgba(30, 136, 229, 0.2);
}

.text-gradient-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.text-gradient-gold {
    background: linear-gradient(135deg, #ffd700 0%, #ffed4e 50%, #ff6b35 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.text-shadow-soft {
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.text-shadow-bold {
    text-shadow: 2px 2px 8px rgba(0, 0, 0, 0.3);
}

/* Animated Text Effects */
@keyframes textShine {
    0% { background-position: -200% center; }
    100% { background-position: 200% center; }
}

.text-shine {
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
    background-size: 200% 100%;
    animation: textShine 3s infinite;
}

/* General Styles */
:root {
    --primary-color: #1e88e5;
    --secondary-color: #1cc88a;
    --dark-color: #2e384d;
    --light-color: #f8f9fc;
    --danger-color: #e74a3b;
    --gray-color: #858796;
    --transition: all 0.3s ease;

    /* Dark Mode Variables */
    --bg-color: #fff;
    --text-color: #333;
    --card-bg: #fff;
    --header-bg: rgba(255, 255, 255, 0.95);
    --footer-bg: var(--dark-color);
    --border-color: #ddd;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: 'Inter', 'Poppins', sans-serif;
    line-height: 1.7;
    color: var(--text-color);
    background-color: var(--bg-color);
    transition: background-color 0.3s ease, color 0.3s ease;
    font-weight: 400;
    letter-spacing: -0.01em;
}

.container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

section {
    padding: 80px 0;
}

.section-header {
    text-align: center;
    margin-bottom: 50px;
}

.section-header h2 {
    font-family: 'Playfair Display', serif;
    font-size: 42px;
    font-weight: 700;
    color: var(--dark-color);
    margin-bottom: 15px;
    position: relative;
    display: inline-block;
    letter-spacing: -0.02em;
    background: linear-gradient(135deg, var(--dark-color) 0%, var(--primary-color) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.section-header h2::after {
    content: '';
    position: absolute;
    width: 50px;
    height: 3px;
    background-color: var(--primary-color);
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
}

.section-header p {
    color: var(--gray-color);
    font-size: 18px;
    font-weight: 400;
    letter-spacing: 0.5px;
    opacity: 0.8;
    font-family: 'Inter', sans-serif;
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.8;
}

.btn {
    display: inline-block;
    padding: 12px 30px;
    border-radius: 50px;
    text-decoration: none;
    font-family: 'Space Grotesk', sans-serif;
    font-weight: 600;
    font-size: 16px;
    letter-spacing: 0.5px;
    transition: var(--transition);
    cursor: pointer;
    border: none;
    text-align: center;
    position: relative;
    overflow: hidden;
    text-transform: uppercase;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.btn-primary {
    background-color: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background-color: #3a5fd9;
    transform: translateY(-3px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.btn-secondary {
    background-color: transparent;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
}

.btn-secondary:hover {
    background-color: var(--primary-color);
    color: white;
    transform: translateY(-3px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

img {
    max-width: 100%;
    height: auto;
}

/* Header Styles */
header {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    background-color: rgba(255, 255, 255, 0.95);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    z-index: 1000;
    padding: 15px 0;
    transition: var(--transition);
}

header.sticky {
    padding: 10px 0;
    background-color: white;
}

header .container {
    display: flex;
    justify-content: flex-end;
    align-items: center;
}

.logo h1 {
    font-size: 28px;
    color: var(--dark-color);
    margin-right: auto;
}

.logo span {
    color: var(--primary-color);
}

.nav-menu {
    display: flex;
    list-style: none;
}

.nav-menu li {
    margin-left: 30px;
}

.nav-menu li a {
    text-decoration: none;
    color: var(--dark-color);
    font-weight: 500;
    transition: var(--transition);
    position: relative;
    font-size: 16px;
    text-transform: capitalize;
}

.nav-menu li a:hover,
.nav-menu li a.active {
    color: var(--primary-color);
}

.nav-menu li a::after {
    content: '';
    position: absolute;
    width: 0;
    height: 2px;
    background-color: var(--primary-color);
    bottom: -5px;
    left: 0;
    transition: var(--transition);
}

.nav-menu li a:hover::after,
.nav-menu li a.active::after {
    width: 100%;
}

.menu-toggle {
    display: none;
    font-size: 24px;
    cursor: pointer;
    color: var(--dark-color);
}

/* Hero Section - Modern Office Style */
.hero {
    height: 100vh;
    background: url('https://images.unsplash.com/photo-1497366754035-f200968a6e72?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2069&q=80') no-repeat center center/cover;
    position: relative;
    display: flex;
    align-items: center;
    text-align: center;
    color: white;
    padding-top: 80px;
    overflow: hidden;
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1;
}

.hero .container {
    position: relative;
    z-index: 2;
}

.hero-content {
    max-width: 800px;
    margin: 0 auto;
}

.hero-content h1 {
    font-family: 'Space Grotesk', sans-serif;
    font-size: 64px;
    font-weight: 700;
    margin-bottom: 20px;
    animation: fadeInDown 1s ease;
    text-shadow: 2px 2px 8px rgba(0, 0, 0, 0.4);
    letter-spacing: -0.03em;
    background: linear-gradient(135deg, #ffffff 0%, #f0f8ff 50%, #e6f3ff 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    position: relative;
}

.hero-content h2 {
    font-family: 'Inter', sans-serif;
    font-size: 32px;
    font-weight: 500;
    margin-bottom: 30px;
    color: var(--secondary-color);
    animation: fadeInDown 1s ease 0.2s;
    animation-fill-mode: both;
    letter-spacing: 0.5px;
    text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.3);
    background: linear-gradient(135deg, var(--secondary-color) 0%, #00d4aa 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-buttons {
    animation: fadeInUp 1s ease 0.4s;
    animation-fill-mode: both;
    margin-top: 20px;
}

.hero-buttons .btn {
    margin: 0 10px;
    padding: 15px 35px;
    font-size: 18px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.hero-wave {
    position: absolute;
    bottom: -1px; /* Ensure no gap between wave and next section */
    left: 0;
    width: 100%;
    height: 120px;
    z-index: 3;
    filter: drop-shadow(0px -2px 3px rgba(0, 0, 0, 0.1));
    overflow: hidden;
}

.hero-wave svg {
    width: 100%;
    height: 100%;
    display: block;
    transform: scale(1.05); /* Slightly scale up to avoid any edge gaps */
}

/* About Section */
.about-content {
    display: flex;
    align-items: center;
    gap: 50px;
}

.about-image {
    flex: 1;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.about-text {
    flex: 1;
}

.about-text h3 {
    font-size: 28px;
    margin-bottom: 20px;
    color: var(--dark-color);
}

.about-text p {
    margin-bottom: 20px;
    color: var(--gray-color);
}

.about-features {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
    margin-top: 30px;
}

.feature {
    display: flex;
    align-items: center;
}

.feature i {
    color: var(--primary-color);
    margin-right: 10px;
    font-size: 18px;
}

/* Services Section */
.services {
    background-color: var(--light-color);
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
}

.service-card {
    background-color: white;
    padding: 30px;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    transition: var(--transition);
    text-align: center;
}

.service-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.service-icon {
    width: 70px;
    height: 70px;
    background-color: var(--primary-color);
    color: white;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 0 auto 20px;
    font-size: 28px;
}

.service-card h3 {
    font-size: 22px;
    margin-bottom: 15px;
    color: var(--dark-color);
}

.service-card p {
    color: var(--gray-color);
}

/* Projects Section */
.projects-filter {
    display: flex;
    justify-content: center;
    margin-bottom: 30px;
    flex-wrap: wrap;
}

.filter-btn {
    background: none;
    border: none;
    padding: 8px 20px;
    margin: 5px;
    cursor: pointer;
    font-size: 16px;
    font-weight: 500;
    color: var(--gray-color);
    transition: var(--transition);
    border-radius: 50px;
}

.filter-btn.active,
.filter-btn:hover {
    background-color: var(--primary-color);
    color: white;
}

.projects-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 30px;
}

.project-card {
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    transition: var(--transition);
}

.project-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.project-img {
    position: relative;
    overflow: hidden;
}

.project-img img {
    width: 100%;
    transition: var(--transition);
}

.project-card:hover .project-img img {
    transform: scale(1.1);
}

.project-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(78, 115, 223, 0.9);
    display: flex;
    justify-content: center;
    align-items: center;
    opacity: 0;
    transition: var(--transition);
}

.project-card:hover .project-overlay {
    opacity: 1;
}

.project-info {
    text-align: center;
    color: white;
    padding: 20px;
}

.project-info h3 {
    font-size: 22px;
    margin-bottom: 10px;
}

.project-info p {
    margin-bottom: 20px;
}

.btn-view-project {
    display: inline-block;
    padding: 8px 20px;
    background-color: white;
    color: var(--primary-color);
    text-decoration: none;
    border-radius: 50px;
    font-weight: 500;
    transition: var(--transition);
}

.btn-view-project:hover {
    background-color: var(--dark-color);
    color: white;
}

/* Team Section */
.team {
    background-color: var(--light-color);
}

.team-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 30px;
    max-width: 900px;
    margin: 0 auto;
}

.team-member {
    background-color: white;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    transition: var(--transition);
}

.team-member:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.member-img {
    overflow: hidden;
}

.member-img img {
    width: 100%;
    transition: var(--transition);
}

.team-member:hover .member-img img {
    transform: scale(1.1);
}

.member-info {
    padding: 20px;
    text-align: center;
}

.member-info h3 {
    font-family: 'Space Grotesk', sans-serif;
    font-size: 26px;
    font-weight: 600;
    margin-bottom: 5px;
    color: var(--dark-color);
    letter-spacing: -0.01em;
    background: linear-gradient(135deg, var(--dark-color) 0%, var(--primary-color) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    position: relative;
    transition: all 0.3s ease;
    cursor: pointer;
}

.member-info h3:hover {
    transform: scale(1.05);
    text-shadow: 0 4px 8px rgba(30, 136, 229, 0.3);
}

.position {
    color: var(--primary-color);
    font-weight: 500;
    margin-bottom: 15px;
}

.bio {
    color: var(--gray-color);
    margin-bottom: 15px;
}

.social-links {
    display: flex;
    justify-content: center;
    gap: 15px;
}

.social-links a {
    width: 35px;
    height: 35px;
    background-color: var(--light-color);
    color: var(--primary-color);
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    transition: var(--transition);
}

.social-links a:hover {
    background-color: var(--primary-color);
    color: white;
}

/* Contact Section */
.contact-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 50px;
}

.contact-info {
    display: grid;
    grid-template-columns: 1fr;
    gap: 30px;
}

.contact-item {
    display: flex;
    align-items: flex-start;
}

.contact-item .icon {
    width: 50px;
    height: 50px;
    background-color: var(--primary-color);
    color: white;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-right: 15px;
    font-size: 20px;
}

/* Instagram icon gradient background */
.contact-item .icon .fa-instagram {
    background: radial-gradient(circle at 30% 107%, #fdf497 0%, #fdf497 5%, #fd5949 45%, #d6249f 60%, #285AEB 90%);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    font-size: 24px;
}

.contact-item .text h3 {
    font-size: 18px;
    margin-bottom: 5px;
    color: var(--dark-color);
}

.contact-item .text p {
    color: var(--gray-color);
}

.contact-item .text p a {
    color: var(--primary-color);
    text-decoration: none;
    transition: var(--transition);
}

.contact-item .text p a:hover {
    color: var(--secondary-color);
    text-decoration: underline;
}

.contact-form {
    background-color: white;
    padding: 30px;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.form-group {
    margin-bottom: 20px;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 12px 15px;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-family: 'Poppins', sans-serif;
    font-size: 16px;
    transition: var(--transition);
}

.form-group input:focus,
.form-group textarea:focus {
    border-color: var(--primary-color);
    outline: none;
}

.form-group textarea {
    height: 150px;
    resize: none;
}

/* Footer */
footer {
    background-color: var(--dark-color);
    color: white;
    padding: 70px 0 0;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 30px;
    margin-bottom: 50px;
}

.footer-logo h2 {
    font-size: 28px;
    margin-bottom: 15px;
}

.footer-logo span {
    color: var(--primary-color);
}

.footer-logo p {
    color: #ccc;
}

.footer-links h3,
.footer-services h3,
.footer-newsletter h3 {
    font-size: 20px;
    margin-bottom: 20px;
    position: relative;
    padding-bottom: 10px;
}

.footer-links h3::after,
.footer-services h3::after,
.footer-newsletter h3::after {
    content: '';
    position: absolute;
    width: 40px;
    height: 2px;
    background-color: var(--primary-color);
    bottom: 0;
    left: 0;
}

.footer-links ul,
.footer-services ul {
    list-style: none;
}

.footer-links ul li,
.footer-services ul li {
    margin-bottom: 10px;
}

.footer-links ul li a,
.footer-services ul li a {
    color: #ccc;
    text-decoration: none;
    transition: var(--transition);
}

.footer-links ul li a:hover,
.footer-services ul li a:hover {
    color: var(--primary-color);
    padding-left: 5px;
}

.footer-newsletter p {
    color: #ccc;
    margin-bottom: 20px;
}

.newsletter-form {
    display: flex;
    margin-bottom: 20px;
}

.newsletter-form input {
    flex: 1;
    padding: 12px 15px;
    border: none;
    border-radius: 5px 0 0 5px;
    font-family: 'Poppins', sans-serif;
}

.newsletter-form button {
    background-color: var(--primary-color);
    color: white;
    border: none;
    padding: 0 20px;
    border-radius: 0 5px 5px 0;
    cursor: pointer;
    transition: var(--transition);
}

.newsletter-form button:hover {
    background-color: #3a5fd9;
}

.footer-bottom {
    text-align: center;
    padding: 20px 0;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

/* Animations */
@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive Styles */
@media screen and (max-width: 991px) {
    .about-content {
        flex-direction: column;
    }

    .about-image, .about-text {
        flex: none;
        width: 100%;
    }

    .about-image {
        margin-bottom: 30px;
    }
}

@media screen and (max-width: 768px) {
    .menu-toggle {
        display: block;
    }

    .nav-menu {
        position: fixed;
        top: 70px;
        left: -100%;
        width: 80%;
        height: calc(100vh - 70px);
        background-color: white;
        flex-direction: column;
        align-items: center;
        padding: 40px 0;
        transition: var(--transition);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }

    .nav-menu.active {
        left: 0;
    }

    .nav-menu li {
        margin: 15px 0;
    }

    .hero-content h1 {
        font-size: 36px;
    }

    .hero-content h2 {
        font-size: 20px;
    }

    .hero-content p {
        font-size: 16px;
    }

    .section-header h2 {
        font-size: 30px;
    }

    .about-features {
        grid-template-columns: 1fr;
    }
}

@media screen and (max-width: 576px) {
    .hero-buttons .btn {
        display: block;
        width: 100%;
        margin: 10px 0;
    }

    .projects-grid {
        grid-template-columns: 1fr;
    }

    .contact-content {
        grid-template-columns: 1fr;
    }
}

@media screen and (max-width: 768px) {
    .team-grid {
        grid-template-columns: 1fr;
    }
}



/* Career Section */
.career {
    background-color: white;
}

.career-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 40px;
}

.career-intro h3 {
    font-size: 28px;
    margin-bottom: 20px;
    color: var(--dark-color);
}

.career-intro p {
    color: var(--gray-color);
    margin-bottom: 30px;
}

.benefits-slider {
    position: relative;
    background-color: var(--light-color);
    border-radius: 15px;
    padding: 30px;
    margin-top: 30px;
    height: 200px;
    overflow: hidden;
}

.benefit-item {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
    padding: 30px;
    opacity: 0;
    transform: translateX(50px);
    transition: all 0.5s ease;
}

.benefit-item.active {
    opacity: 1;
    transform: translateX(0);
}

.benefit-icon {
    font-size: 36px;
    color: var(--primary-color);
    margin-bottom: 15px;
}

.benefit-item h4 {
    font-size: 20px;
    margin-bottom: 10px;
    color: var(--dark-color);
}

.benefit-controls {
    position: absolute;
    bottom: 20px;
    left: 0;
    width: 100%;
    display: flex;
    justify-content: center;
    gap: 10px;
}

.benefit-dot {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background-color: rgba(30, 136, 229, 0.3);
    cursor: pointer;
    transition: all 0.3s ease;
}

.benefit-dot.active {
    background-color: var(--primary-color);
    transform: scale(1.2);
}

.job-listings {
    display: grid;
    grid-template-columns: 1fr;
    gap: 20px;
}

.job-card {
    background-color: white;
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    border-left: 4px solid var(--primary-color);
}

.job-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.job-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.job-header h3 {
    font-size: 20px;
    color: var(--dark-color);
}

.job-type {
    background-color: rgba(30, 136, 229, 0.1);
    color: var(--primary-color);
    padding: 5px 15px;
    border-radius: 50px;
    font-size: 14px;
    font-weight: 500;
}

.job-details {
    display: flex;
    gap: 20px;
    margin-bottom: 15px;
}

.job-details p {
    color: var(--gray-color);
    font-size: 14px;
}

.job-details i {
    margin-right: 5px;
    color: var(--primary-color);
}

.job-description {
    color: var(--gray-color);
    margin-bottom: 20px;
}

/* Testimonials Section */
.testimonials {
    background-color: var(--light-color);
}

.testimonials-slider {
    padding-bottom: 60px;
}

.testimonial-item {
    padding: 20px;
}

.testimonial-content {
    background-color: var(--card-bg);
    padding: 30px;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    text-align: center;
    position: relative;
    transition: var(--transition);
}

.testimonial-content:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.testimonial-img {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    overflow: hidden;
    margin: 0 auto 20px;
    border: 5px solid var(--light-color);
}

.testimonial-img img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.quote {
    font-size: 30px;
    color: var(--primary-color);
    margin-bottom: 15px;
}

.testimonial-content p {
    color: var(--gray-color);
    margin-bottom: 20px;
    font-style: italic;
}

.client-info h4 {
    font-size: 18px;
    margin-bottom: 5px;
    color: var(--dark-color);
}

.client-info p {
    font-style: normal;
    font-size: 14px;
    margin-bottom: 15px;
}

.rating {
    color: #ffc107;
    font-size: 16px;
}

.swiper-pagination-bullet {
    background-color: var(--primary-color);
}

.swiper-button-next,
.swiper-button-prev {
    color: var(--primary-color);
}

/* Stats Counter Section */
.stats {
    background: linear-gradient(rgba(0, 0, 0, 0.8), rgba(0, 0, 0, 0.8)), url('https://via.placeholder.com/1920x500') no-repeat center center/cover;
    padding: 100px 0;
    color: white;
    text-align: center;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 30px;
}

.stat-item {
    padding: 20px;
}

.stat-icon {
    font-size: 40px;
    color: var(--primary-color);
    margin-bottom: 20px;
}

.stat-count {
    font-size: 40px;
    font-weight: 700;
    margin-bottom: 10px;
    display: flex;
    justify-content: center;
    align-items: baseline;
}

.stat-count span {
    display: inline-block;
}

.stat-item h3 {
    font-size: 18px;
    font-weight: 500;
}

/* Back to Top Button */
.back-to-top {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 50px;
    height: 50px;
    background-color: var(--primary-color);
    color: white;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    text-decoration: none;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition);
    z-index: 999;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.back-to-top.active {
    opacity: 1;
    visibility: visible;
}

.back-to-top:hover {
    background-color: var(--dark-color);
    transform: translateY(-5px);
}

/* Dark Mode Toggle */
.dark-mode-toggle {
    position: fixed;
    bottom: 30px;
    left: 30px;
    width: 50px;
    height: 50px;
    background-color: var(--dark-color);
    color: white;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    z-index: 999;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transition: var(--transition);
}

.dark-mode-toggle:hover {
    transform: translateY(-5px);
}

.dark-mode-toggle i {
    font-size: 20px;
    transition: var(--transition);
}

/* Dark Mode Styles */
body.dark-mode {
    --bg-color: #121212;
    --text-color: #f8f9fc;
    --card-bg: #1e1e1e;
    --header-bg: rgba(30, 30, 30, 0.95);
    --footer-bg: #121212;
    --border-color: #333;
}

body.dark-mode .dark-mode-toggle {
    background-color: var(--primary-color);
}

body.dark-mode .dark-mode-toggle i {
    transform: rotate(360deg);
}

/* Light/Dark mode icon transitions */
.dark-mode-toggle i {
    transition: transform 0.5s ease, opacity 0.3s ease;
}

.dark-mode-toggle .fa-sun {
    position: absolute;
    opacity: 0;
    transform: scale(0.5);
}

.dark-mode-toggle .fa-moon {
    opacity: 1;
    transform: scale(1);
}

body.dark-mode .dark-mode-toggle .fa-moon {
    opacity: 0;
    transform: scale(0.5);
}

body.dark-mode .dark-mode-toggle .fa-sun {
    opacity: 1;
    transform: scale(1);
}

body.dark-mode .service-card,
body.dark-mode .project-card,
body.dark-mode .team-member,
body.dark-mode .testimonial-content,
body.dark-mode .contact-form {
    background-color: var(--card-bg);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

body.dark-mode .services,
body.dark-mode .team,
body.dark-mode .testimonials {
    background-color: #1a1a1a;
}

body.dark-mode .form-group input,
body.dark-mode .form-group textarea {
    background-color: #2a2a2a;
    border-color: var(--border-color);
    color: var(--text-color);
}

/* Image Gallery Lightbox */
.lightbox {
    display: none;
    position: fixed;
    z-index: 1100;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.9);
    overflow: hidden;
}

.lightbox.active {
    display: block;
}

.lightbox-content {
    position: relative;
    margin: auto;
    padding: 0;
    width: 80%;
    max-width: 1000px;
    height: 80%;
    display: flex;
    justify-content: center;
    align-items: center;
}

.lightbox-img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
}

.lightbox-caption {
    position: absolute;
    bottom: -40px;
    width: 100%;
    text-align: center;
    color: white;
    font-size: 18px;
}

.lightbox-close {
    position: absolute;
    top: 20px;
    right: 30px;
    color: white;
    font-size: 40px;
    font-weight: bold;
    cursor: pointer;
    z-index: 1101;
}

.lightbox-prev,
.lightbox-next {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    color: white;
    font-size: 30px;
    font-weight: bold;
    cursor: pointer;
    padding: 16px;
    transition: var(--transition);
}

.lightbox-prev {
    left: 20px;
}

.lightbox-next {
    right: 20px;
}

.lightbox-prev:hover,
.lightbox-next:hover {
    background-color: rgba(0, 0, 0, 0.5);
    border-radius: 50%;
}

